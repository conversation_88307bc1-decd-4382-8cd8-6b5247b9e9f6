.speaker-device-dropdown-button {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  color: inherit;
  cursor: pointer;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .device-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  .device-name {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
  }

  .dropdown-arrow {
    color: #8c8c8c;
    font-size: 12px;
    flex-shrink: 0;
  }
}

.speaker-device-dropdown-menu {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 200px;
  max-width: 300px;
  border: 1px solid #d9d9d9;

  .device-option {
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: transparent;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #e6f7ff;
      color: #1890ff;
      font-weight: 500;
    }

    &:focus {
      outline: 2px solid #1890ff;
      outline-offset: -2px;
    }
  }
}

// Dark theme support
[data-lk-theme="dark"] {
  .speaker-device-dropdown-menu {
    background: #1f1f1f;
    border-color: #434343;

    .device-option {
      color: white;

      &:hover {
        background-color: #2f2f2f;
      }

      &.selected {
        background-color: #111b26;
        color: #40a9ff;
      }
    }
  }
}
