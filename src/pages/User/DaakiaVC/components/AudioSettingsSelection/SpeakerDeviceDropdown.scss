// SCSS Variables - Colors from ControlBar.scss and VideoConference.scss
$primary-bg-dark: #000;
$secondary-bg-dark: #1f1f1f;
$tertiary-bg-dark: #2b2b2b;
$quaternary-bg-dark: #2d2d38;
$border-dark: #111;
$border-secondary: #242424;
$accent-blue: #0a84ff;
$accent-blue-hover: #40a9ff;
$white: #fff;
$text-primary: #ffffff;
$text-secondary: #8c8c8c;
$hover-overlay: rgba(255, 255, 255, 0.1);
$shadow-dark: rgba(0, 0, 0, 0.75);

.speaker-device-dropdown-button {
  background: $secondary-bg-dark;
  border: 1px solid $border-secondary;
  border-radius: 6px;
  color: $text-primary;
  cursor: pointer;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  transition: all 0.2s ease;

  &:hover {
    background-color: $hover-overlay;
    border-color: $accent-blue-hover;
  }

  &:focus {
    outline: none;
    border-color: $accent-blue;
    background-color: $hover-overlay;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .device-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  .device-name {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
  }

  .dropdown-arrow {
    color: $text-secondary;
    font-size: 12px;
    flex-shrink: 0;
  }
}

.speaker-device-dropdown-menu {
  background: $primary-bg-dark;
  border-radius: 6px;
  box-shadow: 0px 0px 5px 0px $shadow-dark;
  padding: 4px 0;
  min-width: 200px;
  max-width: 300px;
  border: 1px solid $border-secondary;

  .device-option {
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: transparent;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: $text-primary;

    &:hover {
      background-color: $quaternary-bg-dark;
    }

    &.selected {
      background-color: $accent-blue;
      color: $white;
      font-weight: 500;
    }

    &:focus {
      outline: 2px solid $accent-blue;
      outline-offset: -2px;
    }
  }
}

// Light theme fallback (maintaining original light theme colors)
[data-lk-theme="light"] {
  .speaker-device-dropdown-button {
    background: $white;
    border-color: #d9d9d9;
    color: #000;

    &:hover {
      border-color: $accent-blue-hover;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:focus {
      border-color: $accent-blue;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .speaker-device-dropdown-menu {
    background: $white;
    border-color: #d9d9d9;

    .device-option {
      color: #000;

      &:hover {
        background-color: #f5f5f5;
      }

      &.selected {
        background-color: #e6f7ff;
        color: $accent-blue;
      }
    }
  }
}
