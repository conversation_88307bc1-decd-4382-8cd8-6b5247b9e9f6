.combined-audio-dropdown-button {
  background: transparent;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }
}

.combined-audio-dropdown-menu {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 400px;
  border: 1px solid #d9d9d9;
}

.audio-settings-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.audio-setting-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}



.device-dropdown {
  width: 100%;
  
  .audio-device-dropdown-button,
  .speaker-device-dropdown-button {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  .combined-audio-dropdown-menu {
    min-width: 300px;
    padding: 12px;
  }

  .audio-settings-row {
    flex-direction: column;
    gap: 16px;
  }
}

// Dark theme support
[data-lk-theme="dark"] {
  .combined-audio-dropdown-menu {
    background: #1f1f1f;
    border-color: #434343;
    color: white;
  }

  .audio-setting-label {
    color: #ffffff;
  }

  .device-dropdown {
    .audio-device-dropdown-button,
    .speaker-device-dropdown-button {
      background: #2f2f2f;
      border-color: #434343;
      color: white;

      &:hover {
        border-color: #40a9ff;
      }
    }
  }
}
