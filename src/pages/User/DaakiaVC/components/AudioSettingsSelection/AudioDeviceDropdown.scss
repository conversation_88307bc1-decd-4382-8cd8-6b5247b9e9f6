.audio-device-dropdown {
  position: relative;
  display: inline-block;

  &-button {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    min-width: 180px;
    transition: all 0.2s ease;
    color: inherit;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .device-icon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }

    .device-name {
      flex: 1;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
    }

    .dropdown-arrow {
      color: #8c8c8c;
      font-size: 12px;
      flex-shrink: 0;
    }
  }

  &-menu {
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    max-height: 200px;
    overflow-y: auto;
    padding: 4px;

    .device-option {
      width: 100%;
      text-align: left;
      padding: 8px 12px;
      border: none;
      background: transparent;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 2px;
      color: #333;
      transition: all 0.2s ease;
      font-size: 14px;
      display: block;

      &:hover {
        background-color: #f5f5f5;
      }

      &.selected {
        background-color: #1890ff !important;
        color: white !important;
        font-weight: 500;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
